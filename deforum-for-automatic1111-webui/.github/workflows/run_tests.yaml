name: Tests

on:
  - push
  - pull_request

jobs:
  test:
    name: tests on CPU with empty model
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request' || github.event.pull_request.head.repo.full_name != github.event.pull_request.base.repo.full_name
    steps:
      - name: Checkout a1111
        uses: actions/checkout@v3
        with:
          repository: AUTOMATIC1111/stable-diffusion-webui
          ref: v1.6.0 
      - name: Checkout Controlnet extension
        uses: actions/checkout@v3
        with:
          repository: Mikubill/sd-webui-controlnet
          path: extensions/sd-webui-controlnet          
      - name: Checkout Deforum
        uses: actions/checkout@v3
        with:
          path: extensions/deforum
      - name: Set up Python 3.10
        uses: actions/setup-python@v4
        with:
          python-version: 3.10.6
          cache: pip
          cache-dependency-path: |
            **/requirements*txt
            launch.py
      - name: Install test dependencies
        run: pip install wait-for-it -r extensions/deforum/requirements-dev.txt
        env:
          PIP_DISABLE_PIP_VERSION_CHECK: "1"
          PIP_PROGRESS_BAR: "off"
      - name: Setup environment
        run: python launch.py --skip-torch-cuda-test --exit
        env:
          PIP_DISABLE_PIP_VERSION_CHECK: "1"
          PIP_PROGRESS_BAR: "off"
          TORCH_INDEX_URL: https://download.pytorch.org/whl/cpu
          WEBUI_LAUNCH_LIVE_OUTPUT: "1"
          PYTHONUNBUFFERED: "1"
      - name: Start test server
        run: >
          python -m coverage run
          --data-file=.coverage.server
          launch.py
          --skip-prepare-environment
          --skip-torch-cuda-test
          --test-server
          --do-not-download-clip
          --no-half
          --disable-opt-split-attention
          --use-cpu all
          --api-server-stop
          --deforum-api
          --api     
          2>&1 | tee serverlog.txt &
      - name: Run tests (with  continue-on-error due to mysterious non-zero return code on success)
        continue-on-error: true
        id: runtests
        run: |
          wait-for-it --service 127.0.0.1:7860 -t 600
          cd extensions/deforum
          python -m coverage run --data-file=.coverage.client -m pytest -vv --junitxml=tests/results.xml tests
      - name: Check for test failures (necessary because of continue-on-error above)
        id: testresults
        uses: mavrosxristoforos/get-xml-info@1.1.0
        with:
          xml-file: 'extensions/deforum/tests/results.xml'
          xpath: '//testsuite/@failures'
      - name: Fail if there were test failures
        run: |
          echo "Test failures: ${{ steps.testresults.outputs.info }}"
          [ ${{ steps.testresults.outputs.info }} -eq 0 ]
      - name: Kill test server
        if: always()
        run: curl -vv -XPOST http://127.0.0.1:7860/sdapi/v1/server-stop && sleep 10
      - name: Show coverage
        run: |
          python -m coverage combine .coverage* extensions/deforum/.coverage*
          python -m coverage report -i
          python -m coverage html -i
      - name: Upload main app output
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: serverlog
          path: serverlog.txt
      - name: Upload coverage HTML
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: htmlcov
          path: htmlcov
      - name: Surface failing tests
        if: always()
        uses: pmeier/pytest-results-action@main
        with:
          path: extensions/deforum/tests/results.xml
          summary: true
          display-options: fEX
          fail-on-empty: true
