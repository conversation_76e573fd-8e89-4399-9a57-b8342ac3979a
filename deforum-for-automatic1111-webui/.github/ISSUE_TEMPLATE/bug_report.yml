name: Bug Report
description: Create a bug report for the Deforum extension
title: "[Bug]: "
labels: ["bug"]

body:
  - type: checkboxes
    attributes:
      label: Have you read the latest version of the FAQ?
      description: Please visit the page called FAQ & Troubleshooting on the Deforum wiki in this repository and see if your problem has already been described there.
      options:
        - label: I have visited the FAQ page right now and my issue is not present there
          required: true
  - type: checkboxes
    attributes:
      label: Is there an existing issue for this?
      description: Please search to see if an issue already exists for the bug you encountered (including the closed issues).
      options:
        - label: I have searched the existing issues and checked the recent builds/commits of both this extension and the webui
          required: true
  - type: checkboxes
    attributes:
      label: Are you using the latest version of the Deforum extension?
      description: Please, check if your Deforum is based on the latest repo commit (git log) or update it through the 'Extensions' tab and check if the issue still persist. Otherwise, check this box.
      options:
        - label: I have Deforum updated to the lastest version and I still have the issue.
          required: true
  - type: markdown
    attributes:
      value: |
        *Please fill this form with as much information as possible, don't forget to fill "What OS..." and *provide screenshots if possible**
  - type: markdown
    attributes:
      value: |
        **Forewarning:* if you won't provide the full crash log, your issue will be discarded*
  - type: textarea
    id: what-did
    attributes:
      label: What happened?
      description: Tell us what happened in a very clear and simple way
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: Steps to reproduce the problem
      description: Please provide us with precise step by step information on how to reproduce the bug
      value: |
        1. Go to .... 
        2. Press ....
        3. ...
    validations:
      required: true
  - type: textarea
    id: what-should
    attributes:
      label: What should have happened/how would you fix it?
      description: Tell what you think the normal behavior should be or any ideas on how to solve it
  - type: textarea
    id: what-torch
    attributes:
      label: Torch version
      description: Which Torch version your WebUI is working with. You can find it by looking at the bottom of the page.
    validations:
      required: true
  - type: dropdown
    id: where
    attributes:
      label: On which platform are you launching the webui with the extension?
      multiple: true
      options:
        - Local PC setup (Windows)
        - Local PC setup (Linux)
        - Local PC setup (Mac)
        - Google Colab (The Last Ben's)
        - Google Colab (Other)
        - Cloud server (Linux)
        - Other (please specify in "additional information")
  - type: textarea
    id: deforumsettings
    attributes:
      label: Deforum settings
      description: Send here a link to your used settings file or the latest generated one in the 'outputs/img2img-images/Deforum/' folder (ideally, upload it to GitHub gists).
    validations:
      required: true
  - type: textarea
    id: customsettings
    attributes:
      label: Webui core settings
      description: Send here a link to your ui-config.json file in the core 'stable-diffusion-webui' folder. Notice, if you have 'With img2img, do exactly the amount of steps the slider specified' checked, your issue will be discarded.
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Console logs
      description: Now, it is the most important part which most users fail for the first time! Please provide the **full** cmd/terminal logs from the moment you started the webui (i.e. clicked the launch file or started it from cmd) to the part when your bug happened. If you have an **excellent reason** for not being able to include the log, include the string 'I have a good reason for not including the log' and explain why. Try to include at least the webui commit hash, deforum extension commit hash, pyton version and model you are working with.
      render: Shell
    validations:
      required: true
  - type: textarea
    id: misc
    attributes:
      label: Additional information
      description: Any relevant additional info or context.
