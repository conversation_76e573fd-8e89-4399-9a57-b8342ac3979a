# serializer version: 1
# name: test_post_process_FILM
  '''
  1
  00:00:00,000 --> 00:00:00,050
  F#: 0; Cadence: false; Seed: 1; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.002; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  2
  00:00:00,050 --> 00:00:00,100
  F#: 1; Cadence: false; Seed: 2; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: -1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  3
  00:00:00,100 --> 00:00:00,150
  F#: 2; Cadence: false; Seed: 3; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: -1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  4
  00:00:00,150 --> 00:00:00,200
  F#: 3; Cadence: false; Seed: 4; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  5
  00:00:00,200 --> 00:00:00,250
  F#: 4; Cadence: false; Seed: 5; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  
  '''
# ---
# name: test_post_process_RIFE
  '''
  1
  00:00:00,000 --> 00:00:00,050
  F#: 0; Cadence: false; Seed: 1; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.002; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  2
  00:00:00,050 --> 00:00:00,100
  F#: 1; Cadence: false; Seed: 2; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: -1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  3
  00:00:00,100 --> 00:00:00,150
  F#: 2; Cadence: false; Seed: 3; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: -1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  4
  00:00:00,150 --> 00:00:00,200
  F#: 3; Cadence: false; Seed: 4; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  5
  00:00:00,200 --> 00:00:00,250
  F#: 4; Cadence: false; Seed: 5; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  
  '''
# ---
# name: test_post_process_UPSCALE
  '''
  1
  00:00:00,000 --> 00:00:00,050
  F#: 0; Cadence: false; Seed: 1; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.002; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  2
  00:00:00,050 --> 00:00:00,100
  F#: 1; Cadence: false; Seed: 2; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: -1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  3
  00:00:00,100 --> 00:00:00,150
  F#: 2; Cadence: false; Seed: 3; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: -1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  4
  00:00:00,150 --> 00:00:00,200
  F#: 3; Cadence: false; Seed: 4; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  5
  00:00:00,200 --> 00:00:00,250
  F#: 4; Cadence: false; Seed: 5; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  
  '''
# ---
# name: test_post_process_UPSCALE_FILM
  '''
  1
  00:00:00,000 --> 00:00:00,050
  F#: 0; Cadence: false; Seed: 1; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.002; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  2
  00:00:00,050 --> 00:00:00,100
  F#: 1; Cadence: false; Seed: 2; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: -1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  3
  00:00:00,100 --> 00:00:00,150
  F#: 2; Cadence: false; Seed: 3; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: -1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  4
  00:00:00,150 --> 00:00:00,200
  F#: 3; Cadence: false; Seed: 4; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  5
  00:00:00,200 --> 00:00:00,250
  F#: 4; Cadence: false; Seed: 5; Angle: 0; Tr.C.X: 0.500; Tr.C.Y: 0.500; Zoom: 1.003; TrX: 0; TrY: 0; TrZ: 0; RotX: 0; RotY: 0; RotZ: 0; PerFlT: 0; PerFlP: 0; PerFlG: 0; PerFlFV: 53; Noise: 0.040; StrSch: 0.650; CtrstSch: 1; CFGSch: 7; P2PCfgSch: 1.500; SubSSch: 1; SubSStrSch: 0; CkptSch: model1.ckpt; StepsSch: 25; SeedSch: 1; SamplerSchedule: Euler a; ClipskipSchedule: 2; NoiseMultiplierSchedule: 1.050; MaskSchedule: {video_mask}; NoiseMaskSchedule: {video_mask}; AmountSchedule: 0.050; KernelSchedule: 5; SigmaSchedule: 1; ThresholdSchedule: 0; AspectRatioSchedule: 1; FieldOfViewSchedule: 70; NearSchedule: 200; CadenceFlowFactorSchedule: 1; RedoFlowFactorSchedule: 1; FarSchedule: 10000; HybridCompAlphaSchedule: 0.500; HybridCompMaskBlendAlphaSchedule: 0.500; HybridCompMaskContrastSchedule: 1; HybridCompMaskAutoContrastCutoffHighSchedule: 100; HybridCompMaskAutoContrastCutoffLowSchedule: 0; HybridFlowFactorSchedule: 1; Prompt:  tiny cute swamp bunny, highly detailed, intricate, ultra hd, sharp photo, crepuscular rays, in focus, by tomasz alen kopera --neg
  
  
  '''
# ---
