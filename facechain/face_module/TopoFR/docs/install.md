## [v1.11.0](https://pytorch.org/)

## [v1.9.0](https://pytorch.org/get-started/previous-versions/#linux-and-windows-7)
### Linux and Windows  
```shell
# CUDA 11.1
pip install torch==1.9.0+cu111 torchvision==0.10.0+cu111 torchaudio==0.9.0 -f https://download.pytorch.org/whl/torch_stable.html

# CUDA 10.2
pip install torch==1.9.0+cu102 torchvision==0.10.0+cu102 torchaudio==0.9.0 -f https://download.pytorch.org/whl/torch_stable.html
```
