/* 屏幕宽度大于等于500px的设备 */
@media screen and (min-width: 500px) {
  #chatbot, #chatbot_classic, #chatbot_classic textarea{
    height: 100%;
    max-height: calc(58vw - 200px);
    min-height: calc(58vw - 400px);
  }

  #chatbot .wrap, #chatbot_classic, #chatbot_classic textarea{
    max-height: 100%;
  }

  #container_row {
    flex-direction: row-reverse;
  }
}

/* 屏幕宽度小于500px的设备 */
@media screen and (max-width: 499px) {
  #chatbot , #chatbot_classic, #chatbot_classic textarea{
    height: 300px;
  }

  #chatbot .wrap ,#chatbot_classic, #chatbot_classic textarea{
    max-height: 100%;
  }

  #container_row {
    flex-direction: column;
  }

  #chatbot {
    max-width: 91vw;
  }
}

body, .gradio-container {
  background-color: #fff;
}

.gradio-container.app {
  max-width: none !important;
}

#gallery {
  max-height: 450px;
}

/* status_display */
#status_display > .wrap {
  display: none;
}

#status_display {
  display: flex;
  min-height: 2.5em;
  align-items: flex-end;
  justify-content: flex-end;
  color: rgba(98,74,255,.8);
}
#status_display p {
  font-size: .85em;
  font-family: monospace;
  color: var(--body-text-color-subdued);
}

#container_row button {
  border-radius: 0;
  box-shadow: none;
}

#container_row button.primary {
  border: 1px solid #624aff;
  background: #624aff;
  color: #fff;
}

#container_row button.secondary {
  border: 1px solid rgba(98,74,255,.8);
  color: rgba(98,74,255,.8);
}

#container_row .form {
  border-radius: 0;
}

#chat-input textarea {
  border: 1px solid #e5e7eb;
  border-radius: 0;
}

#chat-examples .gallery {
  text-align: left;
  font-size: 12px;
}

#chat-container {
  flex: 1;
}

#chat-bottom-container button.secondary {
  font-weight: 600;
  font-size: 16px;
}

.robot-info {
  display: flex;
  justify-content: center;
  align-items: center;
}

.robot-info > img {
  width: 20%; 
}

.robot-info .robot-info-text {
  padding: 10px;
  color: #624aff;
}

.uploaded-image-box > img {
  width: 100px;
}

#settings .tab-nav button {
  border: none;
}
#settings .tab-nav {
  border: none;
}
#settings .tabitem {
  border: none;
  padding: 0px;
}

#clear_session_button , #refresh_settings_button, #regenerate_button, #classic_button{
  font-size: 1em;
  font-weight: normal;
}
#chatbot_classic{
  height: 100%;
}

#chatbot {
  border-radius: 0;
  border: 1px solid #e5e7eb;
  box-shadow: var(--shadow-drop);
}

/* 对话气泡 */
#chatbot [class *= "message"] {
  border-radius: var(--radius-md) !important;
  border: none;
  padding: var(--spacing-xl) !important;
  min-height: calc(var(--text-md) * var(--line-lg) + 2 * var(--spacing-xl));
  min-width: calc(var(--text-md) * var(--line-lg) + 2 * var(--spacing-xl));
  white-space: pre-line;
  word-wrap: break-word;
  font-size: 1rem;
  line-height: 1.5rem;
}

#chatbot .bot {
  max-width: 85%;
  border-bottom-left-radius: 0 !important;
  margin-left: 38px;
}

#chatbot .bot::before {
  content: '';
  display: block;
  width: 30px;
  height: 30px;
  background-image: url(//img.alicdn.com/imgextra/i4/O1CN01PRJFWt1PU8nZDGIKZ_!!6000000001843-0-tps-128-128.jpg);
  background-size: cover;
  background-repeat: no-repeat;
  position: absolute;
  top: 5px;
  left: -38px;
}

#chatbot .bot img{
  max-height: 400px !important;
}

#chatbot .user {
  max-width: 85%;
  width: auto !important;
  border-bottom-right-radius: 0 !important;
}

#chatbot .bot >  ol {
  padding-left: 30px;
}
#chatbot .bot >  ul {
  padding-left: 30px;
}

#chatbot summary {
  font-size: 0.9em;
  font-style: italic;
  background-color: var(--secondary-400);
  padding-left: 8px;
}

#chatbot .bot details:not(:last-child) {
  border-bottom: 1px solid #f7f8f9;
}

/* 表格 */
table {
  border-radius: var(--radius-xl) !important;
  margin: 1em 0;
  border-collapse: collapse;
  empty-cells: show;
  font-size: 14px;
}

td,th {
  font-weight: var(--base-text-weight-semibold, 600);
  padding: 6px 13px;
  border: 1px solid #d0d7de;
}
thead {
  background-color: rgba(175,184,193,0.2);
}
thead {
   padding: 6px 13px;
}

table tr:nth-child(2n) {
  background-color: var(--color-canvas-subtle)
}

table img {
  background-color: transparent
}

.codehilite .copy-button {
  display: none;
}

.codehilite {
  background: #1d1d1dfc;
  color: #666;
  page-break-inside: avoid;
  max-width: 100%;
  display: block;
  word-wrap: break-word;
  overflow: auto;
  overflow-x: auto;
  counter-reset: line;
  padding-left: 5px;
}

.codehilitetable .codehilite {
  border: 0px solid #3e3e3ed6;
  box-shadow: inset 0 0 0px #000;
}


pre { line-height: 125%; }
td.linenos .normal { color: #37474F; background-color: #263238; padding-left: 5px; padding-right: 5px; }
span.linenos { color: #37474F; background-color: #263238; padding-left: 5px; padding-right: 5px; }
td.linenos .special { color: #607A86; background-color: #263238; padding-left: 5px; padding-right: 5px; }
span.linenos.special { color: #607A86; background-color: #263238; padding-left: 5px; padding-right: 5px; }
.codehilite .hll { background-color: #2C3B41 }
.codehilite { background: #263238; color: #EEFFFF }
.codehilite .c { color: #546E7A; font-style: italic } /* Comment */
.codehilite .err { color: #FF5370 } /* Error */
.codehilite .esc { color: #89DDFF } /* Escape */
.codehilite .g { color: #EEFFFF } /* Generic */
.codehilite .k { color: #BB80B3 } /* Keyword */
.codehilite .l { color: #C3E88D } /* Literal */
.codehilite .n { color: #EEFFFF } /* Name */
.codehilite .o { color: #89DDFF } /* Operator */
.codehilite .p { color: #89DDFF } /* Punctuation */
.codehilite .ch { color: #546E7A; font-style: italic } /* Comment.Hashbang */
.codehilite .cm { color: #546E7A; font-style: italic } /* Comment.Multiline */
.codehilite .cp { color: #546E7A; font-style: italic } /* Comment.Preproc */
.codehilite .cpf { color: #546E7A; font-style: italic } /* Comment.PreprocFile */
.codehilite .c1 { color: #546E7A; font-style: italic } /* Comment.Single */
.codehilite .cs { color: #546E7A; font-style: italic } /* Comment.Special */
.codehilite .gd { color: #FF5370 } /* Generic.Deleted */
.codehilite .ge { color: #89DDFF } /* Generic.Emph */
.codehilite .gr { color: #FF5370 } /* Generic.Error */
.codehilite .gh { color: #C3E88D } /* Generic.Heading */
.codehilite .gi { color: #C3E88D } /* Generic.Inserted */
.codehilite .go { color: #546E7A } /* Generic.Output */
.codehilite .gp { color: #FFCB6B } /* Generic.Prompt */
.codehilite .gs { color: #FF5370 } /* Generic.Strong */
.codehilite .gu { color: #89DDFF } /* Generic.Subheading */
.codehilite .gt { color: #FF5370 } /* Generic.Traceback */
.codehilite .kc { color: #89DDFF } /* Keyword.Constant */
.codehilite .kd { color: #BB80B3 } /* Keyword.Declaration */
.codehilite .kn { color: #89DDFF; font-style: italic } /* Keyword.Namespace */
.codehilite .kp { color: #89DDFF } /* Keyword.Pseudo */
.codehilite .kr { color: #BB80B3 } /* Keyword.Reserved */
.codehilite .kt { color: #BB80B3 } /* Keyword.Type */
.codehilite .ld { color: #C3E88D } /* Literal.Date */
.codehilite .m { color: #F78C6C } /* Literal.Number */
.codehilite .s { color: #C3E88D } /* Literal.String */
.codehilite .na { color: #BB80B3 } /* Name.Attribute */
.codehilite .nb { color: #82AAFF } /* Name.Builtin */
.codehilite .nc { color: #FFCB6B } /* Name.Class */
.codehilite .no { color: #EEFFFF } /* Name.Constant */
.codehilite .nd { color: #82AAFF } /* Name.Decorator */
.codehilite .ni { color: #89DDFF } /* Name.Entity */
.codehilite .ne { color: #FFCB6B } /* Name.Exception */
.codehilite .nf { color: #82AAFF } /* Name.Function */
.codehilite .nl { color: #82AAFF } /* Name.Label */
.codehilite .nn { color: #FFCB6B } /* Name.Namespace */
.codehilite .nx { color: #EEFFFF } /* Name.Other */
.codehilite .py { color: #FFCB6B } /* Name.Property */
.codehilite .nt { color: #FF5370 } /* Name.Tag */
.codehilite .nv { color: #89DDFF } /* Name.Variable */
.codehilite .ow { color: #89DDFF; font-style: italic } /* Operator.Word */
.codehilite .pm { color: #89DDFF } /* Punctuation.Marker */
.codehilite .w { color: #EEFFFF } /* Text.Whitespace */
.codehilite .mb { color: #F78C6C } /* Literal.Number.Bin */
.codehilite .mf { color: #F78C6C } /* Literal.Number.Float */
.codehilite .mh { color: #F78C6C } /* Literal.Number.Hex */
.codehilite .mi { color: #F78C6C } /* Literal.Number.Integer */
.codehilite .mo { color: #F78C6C } /* Literal.Number.Oct */
.codehilite .sa { color: #BB80B3 } /* Literal.String.Affix */
.codehilite .sb { color: #C3E88D } /* Literal.String.Backtick */
.codehilite .sc { color: #C3E88D } /* Literal.String.Char */
.codehilite .dl { color: #EEFFFF } /* Literal.String.Delimiter */
.codehilite .sd { color: #546E7A; font-style: italic } /* Literal.String.Doc */
.codehilite .s2 { color: #C3E88D } /* Literal.String.Double */
.codehilite .se { color: #EEFFFF } /* Literal.String.Escape */
.codehilite .sh { color: #C3E88D } /* Literal.String.Heredoc */
.codehilite .si { color: #89DDFF } /* Literal.String.Interpol */
.codehilite .sx { color: #C3E88D } /* Literal.String.Other */
.codehilite .sr { color: #89DDFF } /* Literal.String.Regex */
.codehilite .s1 { color: #C3E88D } /* Literal.String.Single */
.codehilite .ss { color: #89DDFF } /* Literal.String.Symbol */
.codehilite .bp { color: #89DDFF } /* Name.Builtin.Pseudo */
.codehilite .fm { color: #82AAFF } /* Name.Function.Magic */
.codehilite .vc { color: #89DDFF } /* Name.Variable.Class */
.codehilite .vg { color: #89DDFF } /* Name.Variable.Global */
.codehilite .vi { color: #89DDFF } /* Name.Variable.Instance */
.codehilite .vm { color: #82AAFF } /* Name.Variable.Magic */
.codehilite .il { color: #F78C6C } /* Literal.Number.Integer.Long */