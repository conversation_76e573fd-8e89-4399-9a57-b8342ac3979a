name: Bug Report
description: Create a bug report
title: "[Bug]: "
labels: ["bug-report"]

body:
  - type: checkboxes
    attributes:
      label: Is there an existing issue for this?
      description: Please search both open issues and closed issues to see if an issue already exists for the bug you encountered, and that it hasn't been fixed in a recent build/commit.
      options:
        - label: I have searched the existing issues and checked the recent builds/commits of both this extension and the webui
          required: true
  - type: checkboxes
    attributes:
      label: Have you read FAQ on README?
      description: I have collected some common questions from AnimateDiff original repository.
      options:
        - label: I have updated WebUI and this extension to the latest version
          required: true
  - type: markdown
    attributes:
      value: |
        *Please fill this form with as much information as possible, don't forget to fill "What OS..." and "What browsers" and *provide screenshots if possible**
  - type: textarea
    id: what-did
    attributes:
      label: What happened?
      description: Tell us what happened in a very clear and simple way
    validations:
      required: true
  - type: textarea
    id: steps
    attributes:
      label: Steps to reproduce the problem
      description: Please provide us with precise step by step information on how to reproduce the bug
      value: |
        1. Go to .... 
        2. Press ....
        3. ...
    validations:
      required: true
  - type: textarea
    id: what-should
    attributes:
      label: What should have happened?
      description: Tell what you think the normal behavior should be
    validations:
      required: true
  - type: textarea
    id: commits
    attributes:
      label: Commit where the problem happens
      description: Which commit of the extension are you running on? Please include the commit of both the extension and the webui (Do not write *Latest version/repo/commit*, as this means nothing and will have changed by the time we read your issue. Rather, copy the **Commit** link at the bottom of the UI, or from the cmd/terminal if you can't launch it.)
      value: |
        webui: 
        extension: 
    validations:
      required: true
  - type: dropdown
    id: browsers
    attributes:
      label: What browsers do you use to access the UI ?
      multiple: true
      options:
        - Mozilla Firefox
        - Google Chrome
        - Brave
        - Apple Safari
        - Microsoft Edge
  - type: textarea
    id: cmdargs
    attributes:
      label: Command Line Arguments
      description: Are you using any launching parameters/command line arguments (modified webui-user .bat/.sh) ? If yes, please write them below. Write "No" otherwise.
      render: Shell
    validations:
      required: true
  - type: textarea
    id: logs
    attributes:
      label: Console logs
      description: Please provide the errors printed on your console log of your browser (type F12 and go to console) and your terminal, after your bug happened.
      render: Shell
    validations:
      required: true
  - type: textarea
    id: misc
    attributes:
      label: Additional information
      description: Please provide us with any relevant additional info or context.
